import { NewProfile, Profile, User } from '../schemas';

export interface IProfileRepository {
  findByUserId(userId: number): Promise<Profile | null>;
  findByUserIdWithUserInfo(
    userId: number,
  ): Promise<{ profile: Profile; user: Partial<User> } | null>;
  create(profileData: NewProfile): Promise<Profile>;
  update(id: number, profileData: Partial<Profile>): Promise<Profile | null>;
  delete(id: number): Promise<boolean>;
}
