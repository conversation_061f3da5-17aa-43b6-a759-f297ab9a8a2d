import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class RequestMiddleware implements NestMiddleware {
  private readonly logger = new Logger(RequestMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    // Add request metadata
    const requestId = uuidv4();
    const timestamp = new Date().toISOString();
    const startTime = Date.now();

    // Attach metadata to request object
    req['requestId'] = requestId;
    req['timestamp'] = timestamp;
    req['startTime'] = startTime;

    // Log incoming request
    this.logger.warn(
      `Incoming ${req.method} request to ${req.originalUrl} [RequestId: ${requestId}]`,
    );

    // Handle response finish
    res.on('finish', () => {
      const duration = Date.now() - startTime;
      this.logger.log(
        `${req.method} ${req.originalUrl} - Status: ${res.statusCode} - Duration: ${duration}ms [RequestId: ${requestId}]`,
      );
    });

    next();
  }
}
