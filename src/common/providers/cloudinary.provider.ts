import { ConfigService } from '@nestjs/config';
import cloudinary from 'cloudinary';

export const CloudinaryProvider = {
  provide: 'CLOUDINARY',
  useFactory: (configService: ConfigService) => {
    return cloudinary.v2.config({
      cloud_name: configService.get('cloudinary.cloudName')!,
      api_key: configService.get('cloudinary.apiKey'),
      api_secret: configService.get('cloudinary.apiSecret'),
    });
  },
  inject: [ConfigService],
};
