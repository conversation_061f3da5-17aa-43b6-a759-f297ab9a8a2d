import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { profiles, Profile, NewProfile, User, users } from '../schemas';
import * as schema from '../schemas/profile';
import { IProfileRepository } from '../interfaces/profile-repository.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class ProfileRepository implements IProfileRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}
  async findByUserId(userId: number): Promise<Profile | null> {
    const profile = await this.db
      .select()
      .from(profiles)
      .where(eq(profiles.userId, userId))
      .limit(1);
    return profile[0] || null;
  }
  async findByUserIdWithUserInfo(
    userId: number,
  ): Promise<{ profile: Profile; user: User } | null> {
    const result = await this.db
      .select({
        profile: {
          id: profiles.id,
          firstName: profiles.firstName,
          lastName: profiles.lastName,
          code: profiles.code,
          userId: profiles.userId,
          bio: profiles.bio,
          avatar: profiles.avatar,
          createdAt: profiles.createdAt,
          updatedAt: profiles.updatedAt,
        },
        user: {
          id: users.id,
          name: users.name,
          email: users.email,
          role: users.role,
          googleId: users.googleId,
          isActive: users.isActive,
          isVerified: users.isVerified,
          isDeleted: users.isDeleted,
          isBanned: users.isBanned,
          bannedAt: users.bannedAt,
          deletedAt: users.deletedAt,
          verifiedAt: users.verifiedAt,
          password: users.password,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        },
      })
      .from(profiles)
      .where(eq(profiles.userId, userId))
      .innerJoin(users, eq(profiles.userId, users.id))
      .limit(1);

    return result[0] || null;
  }

  async create(profileData: NewProfile): Promise<Profile> {
    const result = await this.db
      .insert(profiles)
      .values({
        ...profileData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async update(
    id: number,
    profileData: Partial<Profile>,
  ): Promise<Profile | null> {
    const result = await this.db
      .update(profiles)
      .set({
        ...profileData,
        updatedAt: new Date(),
      })
      .where(eq(profiles.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(profiles)
      .where(eq(profiles.id, id))
      .returning({ id: profiles.id });

    return result.length > 0;
  }
}
