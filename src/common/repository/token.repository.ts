import { Injectable, Inject } from '@nestjs/common';
import { eq } from 'drizzle-orm';
import { tokens, Token, NewToken, users } from '../schemas';
import * as schema from '../schemas/token';
import { ITokenRepository } from '../interfaces/token-repository.interface';
import { DATABASE_CONNECTION } from 'src/configs';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';

@Injectable()
export class TokenRepository implements ITokenRepository {
  constructor(
    @Inject(DATABASE_CONNECTION)
    private readonly db: NodePgDatabase<typeof schema>,
  ) {}

  async create(tokenData: NewToken): Promise<Token> {
    const result = await this.db
      .insert(tokens)
      .values({
        ...tokenData,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return result[0];
  }

  async findByToken(token: string): Promise<Token | null> {
    const result = await this.db
      .select()
      .from(tokens)
      .where(eq(tokens.token, token))
      .limit(1);

    return result[0] || null;
  }

  async findByTokenWithUser(
    token: string,
  ): Promise<{ token: Token; user: any } | null> {
    const result = await this.db
      .select({
        token: tokens,
        user: users,
      })
      .from(tokens)
      .where(eq(tokens.token, token))
      .innerJoin(users, eq(tokens.userId, users.id))
      .limit(1);

    return result[0] || null;
  }

  async update(id: number, tokenData: Partial<Token>): Promise<Token | null> {
    const result = await this.db
      .update(tokens)
      .set({
        ...tokenData,
        updatedAt: new Date(),
      })
      .where(eq(tokens.id, id))
      .returning();

    return result[0] || null;
  }

  async delete(id: number): Promise<boolean> {
    const result = await this.db
      .delete(tokens)
      .where(eq(tokens.id, id))
      .returning();

    return result.length > 0;
  }
}
