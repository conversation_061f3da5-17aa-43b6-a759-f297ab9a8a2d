import { varchar } from 'drizzle-orm/pg-core';
import { integer } from 'drizzle-orm/pg-core';
import { serial } from 'drizzle-orm/pg-core';
import { pgTable } from 'drizzle-orm/pg-core';
import { users } from './users';
import { transactions } from './transaction';
import { relations } from 'drizzle-orm';

export const files = pgTable('files', {
  id: serial('id').primaryKey(),
  back: varchar('front', { length: 255 }),
  front: varchar('back', { length: 255 }),
  userId: integer('user_id').references(() => users.id),
  transactionId: integer('transaction_id').references(() => transactions.id),
});

export const fileRelations = relations(files, ({ one }) => ({
  transaction: one(transactions, {
    fields: [files.transactionId],
    references: [transactions.id],
  }),
  user: one(users, {
    fields: [files.userId],
    references: [users.id],
  }),
}));
