import { text } from 'drizzle-orm/pg-core';
import { integer } from 'drizzle-orm/pg-core';
import {
  pgTable,
  serial,
  varchar,
  timestamp,
  boolean,
} from 'drizzle-orm/pg-core';
import { pgEnum } from 'drizzle-orm/pg-core';
import { users } from './users';
import { relations } from 'drizzle-orm';

export const paymentMethodTypeEnum = pgEnum('payment_method_type', [
  'mobile_money',
  'master_card',
  'visa_card',
]);

export const paymentMethods = pgTable('payment_methods', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  type: paymentMethodTypeEnum('type').notNull(),
  currency: varchar('currency', { length: 255 }),
  expireDate: varchar('expire_date', { length: 255 }),
  cvv: varchar('cvv', { length: 255 }),
  idDefault: boolean('is_default').default(false),
  userId: integer('user_id').references(() => users.id),
  accountNumber: varchar('account_number', { length: 255 }),
  countryCode: text('country_code'),
  accountName: varchar('account_name', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});

export const paymentMethodRelation = relations(paymentMethods, ({ one }) => ({
  user: one(users, {
    fields: [paymentMethods.userId],
    references: [users.id],
  }),
}));
