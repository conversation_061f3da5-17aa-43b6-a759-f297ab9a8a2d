import * as crypto from 'crypto';

export function generateRandomCode(length: number = 6): string {
  const min = Math.pow(10, length - 1);
  const max = Math.pow(10, length) - 1;
  const randomNumber = Math.floor(Math.random() * (max - min + 1)) + min;

  return `HEX${randomNumber}`;
}

export function generateOTPCode(length: number = 6): string {
  const buffer = crypto.randomBytes(Math.ceil(length / 2));
  const otp = buffer.toString('hex').toUpperCase().slice(0, length);
  return otp;
}
