import {
  Injectable,
  InternalServerErrorException,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import {
  ProfileRepository,
  UserRepository,
  TokenRepository,
} from 'src/common/repository';
import { LoginDto } from './dto/login.dto';
import { User } from 'src/common/schemas';
import { SignUpDto } from './dto/signup.dto';
import { EmailDto } from './dto/email.dto';
import { ResetPasswordDto } from './dto/reset-password.dto';
import { generateOTPCode, generateRandomCode } from 'src/common/utils';
import { TokenDto } from './dto/token.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersRepository: UserRepository,
    private readonly profileRepository: ProfileRepository,
    private readonly tokenRepository: TokenRepository,
    private readonly jwtService: JwtService,
  ) {}

  async validateUser(dto: LoginDto) {
    const user = await this.usersRepository.findByEmail(dto.email);
    if (!user) throw new UnauthorizedException('Invalid credentials');

    if (user.isBanned) throw new UnauthorizedException('User is banned');
    if (user.isDeleted) throw new UnauthorizedException('User is deleted');
    if (!user.isActive) throw new UnauthorizedException('User is not active');

    const isPasswordValid = await bcrypt.compare(dto.password, user.password);
    if (!isPasswordValid)
      throw new UnauthorizedException('Invalid credentials');

    return this.login(user);
  }

  async signUp(dto: SignUpDto) {
    try {
      const existingUser = await this.usersRepository.findByEmail(dto.email);
      if (existingUser && (existingUser.isBanned || existingUser.isDeleted))
        throw new UnauthorizedException(
          'Please contact support to restore your account',
        );

      if (existingUser) throw new UnauthorizedException('Email already exists');

      const hashedPassword = await bcrypt.hash(dto.password, 10);
      const user = await this.usersRepository.create({
        name: dto.name,
        email: dto.email,
        password: hashedPassword,
      });
      const code = generateRandomCode(6);

      if (!user) return;
      const profile = await this.profileRepository.create({
        avatar: 'https://avatar.iran.liara.run/public/37',
        firstName: dto.name.split(' ')[0],
        lastName: dto.name.split(' ')[1],
        userId: user.id,
        code,
      });

      return { user, profile };
    } catch (err) {
      throw new InternalServerErrorException(err);
    }
  }

  async resetPassword({ token, password }: ResetPasswordDto) {
    const tokenData = await this.tokenRepository.findByToken(token);

    if (!tokenData) throw new UnauthorizedException('Invalid token');

    if (tokenData.expiresAt < new Date()) {
      await this.tokenRepository.delete(tokenData.id);
      throw new UnauthorizedException('Token expired');
    }

    const user = await this.usersRepository.update(tokenData.userId!, {
      password: await bcrypt.hash(password, 10),
    });

    if (user) await this.tokenRepository.delete(tokenData.id);

    return user;
  }

  async forgotPassword({ email }: EmailDto) {
    const user = await this.usersRepository.findByEmail(email);
    if (!user) throw new NotFoundException('User not found');

    const token = await this.tokenRepository.create({
      userId: user.id,
      token: generateOTPCode(6),
      type: 'reset_password',
      expiresAt: new Date(Date.now() + 10 * 60 * 1000),
    });

    if (token) {
      // send email
    }

    return { message: 'Password updated successfully' };
  }

  async sendveficationToken({ email }: EmailDto) {
    const user = await this.usersRepository.findByEmail(email);
    if (!user) throw new NotFoundException('User not found');

    const token = await this.tokenRepository.create({
      userId: user.id,
      token: generateOTPCode(6),
      type: 'verification',
      expiresAt: new Date(Date.now() + 10 * 60 * 1000),
    });

    if (token) {
      // send email
    }
  }

  async verifyUsersAccount({ token }: TokenDto) {
    const tokenData = await this.tokenRepository.findByTokenWithUser(token);

    if (!tokenData) throw new UnauthorizedException('Invalid token');

    if (tokenData.token.expiresAt < new Date()) {
      await this.tokenRepository.delete(tokenData.token.id);
      throw new UnauthorizedException('Token expired');
    }

    const user = await this.usersRepository.update(tokenData.token.userId!, {
      isVerified: true,
      verifiedAt: new Date(),
    });

    if (user) await this.tokenRepository.delete(tokenData.token.id);

    return user;
  }

  login(user: Partial<User>) {
    const payload = { id: user.id, email: user.email, role: user.role };
    return {
      access_token: this.jwtService.sign(payload),
    };
  }
}
