import { Module } from '@nestjs/common';
import { UsersModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { PaymentModule } from './payment/payment.module';
import { KycModule } from './kyc/kyc.module';
import { AccountsModule } from './payment/accounts/accounts.module';
import { NotificationsService } from './notifications/notifications.service';
import { NotificationsModule } from './notifications/notifications.module';
import { EmailsModule } from './emails/emails.module';
import { SmsModule } from './sms/sms.module';
import { FilesModule } from './files/files.module';

@Module({
  imports: [
    UsersModule,
    AuthModule,
    PaymentModule,
    KycModule,
    AccountsModule,
    NotificationsModule,
    EmailsModule,
    SmsModule,
    FilesModule,
  ],
  providers: [NotificationsService],
})
export class CoreModule {}
