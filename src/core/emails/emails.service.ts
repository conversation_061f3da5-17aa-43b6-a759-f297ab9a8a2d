import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { EmailConfig } from 'src/common/interfaces';

@Injectable()
export class EmailsService {
  private readonly logger = new Logger(EmailsService.name);
  private transporter: nodemailer.Transporter;

  constructor(private readonly configService: ConfigService) {
    this.createTransporter();
  }

  private createTransporter() {
    const emailConfig = this.configService.get<EmailConfig>('email');
    if (!emailConfig) throw new Error('Email configuration not found');
    this.transporter = nodemailer.createTransport({
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      auth: {
        user: emailConfig.auth.user,
        pass: emailConfig.auth.pass,
      },
    });
    // Verify connection configuration
    this.transporter.verify((error) => {
      if (error) this.logger.error('Email configuration error:', error);
      else this.logger.log('Email service is ready to send messages');
    });
  }
}
