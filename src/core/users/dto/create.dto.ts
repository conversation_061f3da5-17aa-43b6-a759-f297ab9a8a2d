import { <PERSON><PERSON><PERSON>, <PERSON>N<PERSON>Empty, <PERSON>S<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiProperty({ example: '<PERSON>', description: 'User full name' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    example: 'user',
    description: 'User role',
    enum: ['admin', 'user', 'super_admin'],
    default: 'user',
  })
  @IsString()
  @IsNotEmpty()
  role: 'admin' | 'user' | 'super_admin' = 'user';

  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    example: 'password123',
    description: 'User password (minimum 6 characters)',
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(6)
  password: string;
}
