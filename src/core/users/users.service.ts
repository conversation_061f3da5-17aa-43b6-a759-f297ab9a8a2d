import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { PaginationQuery } from 'src/common/interfaces';
import { ProfileRepository, UserRepository } from 'src/common/repository';
import { CreateUserDto } from './dto/create.dto';
import * as bcrypt from 'bcrypt';
import { UpdateUserDto } from './dto/update.dto';
import { generateRandomCode } from 'src/common/utils';

@Injectable()
export class UsersService {
  constructor(
    private readonly usersRepository: UserRepository,
    private readonly profileRepository: ProfileRepository,
  ) {}

  async createUser(dto: CreateUserDto) {
    try {
      const existingUser = await this.usersRepository.findByEmail(dto.email);
      if (existingUser) throw new BadRequestException('Email already exists');
      const hashedPassword = await bcrypt.hash(dto.password, 10);

      const user = await this.usersRepository.create({
        role: dto.role,
        name: dto.name,
        email: dto.email,
        password: hashedPassword,
      });

      const code = generateRandomCode(6);

      if (!user) return;
      const profile = await this.profileRepository.create({
        avatar: 'https://avatar.iran.liara.run/public/37',
        firstName: dto.name.split(' ')[0],
        lastName: dto.name.split(' ')[1],
        userId: user.id,
        code,
      });

      return { user, profile };
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async updateUser(id: number, dto: UpdateUserDto) {
    try {
      const existingUser = await this.getUserById(id);
      const user = await this.usersRepository.update(existingUser.id, dto);
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getAllUsers(query: PaginationQuery) {
    try {
      const data = await this.usersRepository.findAll(query);
      return data;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getUserById(id: number) {
    try {
      const user = await this.usersRepository.findById(id);
      if (!user) throw new NotFoundException();
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getUserByEmail(email: string) {
    try {
      const user = await this.usersRepository.findByEmail(email);
      if (!user) throw new NotFoundException();
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async getActiveUsers() {
    try {
      const users = await this.usersRepository.findActiveUsers();
      return users;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async deactivateUser(id: number) {
    try {
      const existingUser = await this.getUserById(id);
      const user = await this.usersRepository.deactivateUser(existingUser.id);
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async banUser(id: number) {
    try {
      const existingUser = await this.getUserById(id);
      const user = await this.usersRepository.bandUser(existingUser.id);
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async restoreUser(id: number) {
    try {
      const existingUser = await this.getUserById(id);
      const user = await this.usersRepository.restoreUser(existingUser.id);
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async softDeleteUser(id: number) {
    try {
      const existingUser = await this.getUserById(id);
      const user = await this.usersRepository.softDeleteUser(existingUser.id);
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }

  async permanentDelete(id: number) {
    try {
      const existingUser = await this.getUserById(id);
      const user = await this.usersRepository.delete(existingUser.id);
      return user;
    } catch (error) {
      throw new InternalServerErrorException(error);
    }
  }
}
