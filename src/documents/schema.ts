import { pgTable, serial, varchar, timestamp } from 'drizzle-orm/pg-core';

export const documents = pgTable('documents', {
  id: serial('id').primaryKey(),
  //   userId: varchar('user_id', { length: 255 }).unique(),
  documentType: varchar('document_type', { length: 255 }).notNull(),
  frontUrl: varchar('front_url', { length: 255 }),
  backUrl: varchar('back_url', { length: 255 }),
  createdAt: timestamp('created_at').defaultNow(),
  updatedAt: timestamp('updated_at').defaultNow(),
});
